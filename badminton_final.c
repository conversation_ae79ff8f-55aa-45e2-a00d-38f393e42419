#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#define MAX_COURTS 8
#define FILENAME "Reserve.txt"

typedef struct {
    char id[20];
    char name[50];
    char phone[15];
} User;

typedef struct Node {
    int court;
    int year;
    int month;
    int day;
    int hour;
    User user;
    struct Node* next;
} Node;

Node* head = NULL;

int check_time(int year, int month, int day, int hour);
int get_weekday(int year, int month, int day);
int add_node(int court, int year, int month, int day, int hour, User user);
int check_phone(char* phone);
int check_date(int year, int month, int day);
int check_id(char* id);
void save_file();
void load_file();


void add_booking();
void show_status();
void search_info();
void cancel_booking();

int main() {
    printf("========================================\n");
    printf("欢迎使用上海理工大学1100校区羽毛球场预约系统！\n");
    printf("========================================\n");
    printf("正在加载数据...\n");

    load_file();
    printf("数据加载完成！\n");

    int choice;
    do {
        printf("\n========================================\n");
        printf("  上海理工大学1100校区羽毛球场预约系统  \n");
        printf("========================================\n");
        printf("1. 信息录入 - 预约场地\n");
        printf("2. 信息输出 - 查看三天内场地状态\n");
        printf("3. 信息查询 - 按场地或手机号查询\n");
        printf("4. 信息修改 - 取消预约\n");
        printf("0. 退出系统\n");
        printf("========================================\n");
        printf("请选择功能(0-4): ");

        scanf("%d", &choice);

        switch (choice) {
            case 1:
                add_booking();
                break;
            case 2:
                show_status();
                break;
            case 3:
                search_info();
                break;
            case 4:
                cancel_booking();
                break;
            case 0:
                printf("\n正在退出系统...\n");
                printf("感谢使用羽毛球场预约系统！\n");
                printf("再见！\n");
                break;
            default:
                printf("无效选择！请输入0-4之间的数字。\n");
                break;
        }
    } while (choice != 0);

    return 0;
}

int get_weekday(int year, int month, int day) {
    if (month < 3) {
        month += 12;
        year--;
    }
    int k = year % 100;
    int j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

int check_time(int year, int month, int day, int hour) {
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    if (year < 2024 || year > 2030) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > 31) return 0;
    if (hour < 0 || hour > 23) return 0;

    if (year < t->tm_year + 1900) return 0;
    if (year == t->tm_year + 1900 && month < t->tm_mon + 1) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day < t->tm_mday) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day == t->tm_mday && hour < t->tm_hour) return 0;

    int weekday = get_weekday(year, month, day);

    if (weekday >= 1 && weekday <= 5) {
        if (hour < 15 || hour >= 21) return 0;
        if ((weekday == 1 || weekday == 3 || weekday == 5) && hour >= 17 && hour < 20) return 0;
    } else {
        if (hour < 9 || hour >= 21) return 0;
    }

    return 1;
}

int add_node(int court, int year, int month, int day, int hour, User user) {
    if (court < 1 || court > MAX_COURTS) {
        return -3;
    }

    Node* current = head;
    while (current != NULL) {
        if (current->court == court && current->year == year &&
            current->month == month && current->day == day && current->hour == hour) {
            return -1;
        }
        current = current->next;
    }

    Node* new_node = (Node*)malloc(sizeof(Node));
    if (new_node == NULL) {
        return -2;
    }

    new_node->court = court;
    new_node->year = year;
    new_node->month = month;
    new_node->day = day;
    new_node->hour = hour;
    new_node->user = user;

    new_node->next = head;
    head = new_node;

    return 0;
}

int check_phone(char* phone) {
    int len = strlen(phone);
    if (len != 11) return 0;

    if (phone[0] != '1') return 0;

    for (int i = 0; i < len; i++) {
        if (phone[i] < '0' || phone[i] > '9') return 0;
    }

    return 1;
}

int check_date(int year, int month, int day) {
    if (year < 2024 || year > 2030) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > 31) return 0;

    int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    if (month == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) {
        if (day > 29) return 0;
    } else {
        if (day > days_in_month[month - 1]) return 0;
    }

    return 1;
}

int check_id(char* id) {
    int len = strlen(id);
    if (len !=10) return 0;

    for (int i = 0; i < len; i++) {
        if (!(id[i] >= '0' && id[i] <= '9') && !(id[i] >= 'A' && id[i] <= 'Z') && !(id[i] >= 'a' && id[i] <= 'z')) {
            return 0;
        }
    }

    return 1;
}

void save_file() {
    FILE* file = fopen(FILENAME, "w");
    if (file == NULL) {
        printf("无法保存文件！\n");
        return;
    }

    Node* current = head;
    while (current != NULL) {
        fprintf(file, "%d|%d|%d|%d|%d|%s|%s|%s\n",
                current->court, current->year, current->month, current->day, current->hour,
                current->user.id, current->user.name, current->user.phone);
        current = current->next;
    }

    fclose(file);
}

void load_file() {
    FILE* file = fopen(FILENAME, "r");
    if (file == NULL) {
        return;
    }

    char line[200];
    while (fgets(line, sizeof(line), file)) {
        int court, year, month, day, hour;
        User user;

        int result = sscanf(line, "%d|%d|%d|%d|%d|%s|%s|%s",
                           &court, &year, &month, &day, &hour,
                           user.id, user.name, user.phone);

        if (result == 8) {
            add_node(court, year, month, day, hour, user);
        }
    }

    fclose(file);
}

void add_booking() {
    printf("\n=== 预约场地 ===\n");

    User user;
    int court, year, month, day, hour;

    do {
        printf("请输入工号/学号: ");
        scanf("%19s", user.id);  // 限制长度，防止溢出
        // 清空输入缓冲区
        int c;
        while ((c = getchar()) != '\n' && c != EOF);

        if (!check_id(user.id)) {
            printf("工号/学号格式错误！\n");
        }
    } while (!check_id(user.id));

    printf("请输入姓名: ");
    scanf("%19s", user.name);  // 限制长度，防止溢出
    // 清空输入缓冲区
    int c2;
    while ((c2 = getchar()) != '\n' && c2 != EOF);


    do {
        printf("请输入手机号: ");
        scanf("%11s", user.phone);  // 限制长度为11位
        // 清空输入缓冲区
        int c3;
        while ((c3 = getchar()) != '\n' && c3 != EOF);

        if (!check_phone(user.phone)) {
            printf("手机号格式错误！请输入11位数字，以1开头。\n");
        }
    } while (!check_phone(user.phone));

    do {
        printf("请输入场地编号(1-8): ");
        scanf("%d", &court);
        if (court < 1 || court > MAX_COURTS) {
            printf("场地编号无效！请输入1-8之间的数字。\n");
        }
    } while (court < 1 || court > MAX_COURTS);

    do {
        printf("请输入年份(如2025): ");
        scanf("%d", &year);
        if (year < 2024 || year > 2031) {
            printf("年份无效！请输入2025-2030之间的年份。\n");
        }
    } while (year < 2024 || year > 2030);

    do {
        printf("请输入月份(1-12): ");
        scanf("%d", &month);
        if (month < 1 || month > 12) {
            printf("月份无效！请输入1-12之间的数字。\n");
        }
    } while (month < 1 || month > 12);

    do {
        printf("请输入日期(1-31): ");
        scanf("%d", &day);
        if (!check_date(year, month, day)) {
            printf("日期无效！请输入正确的日期。\n");
        }
    } while (!check_date(year, month, day));

    do {
        printf("请输入小时(0-23): ");
        scanf("%d", &hour);
        if (hour < 0 || hour > 23) {
            printf("小时无效！请输入0-23之间的数字。\n");
        }
    } while (hour < 0 || hour > 23);

    if (!check_time(year, month, day, hour)) {
        printf("预约失败：时间不在开放时间内或已过预约时间！\n");
        printf("开放时间：\n");
        printf("周一~周五：15:00-21:00（周一、三、五17:00-20:00为教工活动时间）\n");
        printf("周六~周日：09:00-21:00\n");
        return;
    }

    int result = add_node(court, year, month, day, hour, user);
    if (result == 0) {
        printf("预约成功！\n");
        printf("预约信息：场地%d - %d年%d月%d日 %02d:00 - %s\n",
               court, year, month, day, hour, user.name);
        printf("保存信息：工号=%s, 姓名=%s, 电话=%s\n", user.id, user.name, user.phone);
        save_file();
        printf("数据已保存到文件\n");
    } else if (result == -1) {
        printf("预约失败：该时间段已被预约！\n");
    } else if (result == -2) {
        printf("预约失败：系统内存不足！\n");
    } else if (result == -3) {
        printf("预约失败：场地编号无效！\n");
    } else {
        printf("预约失败：未知错误！\n");
    }
}

void show_status() {
    printf("\n=== 三天内场地状态 ===\n");

    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    char* weekday_names[] = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    for (int day_offset = 0; day_offset < 3; day_offset++) {
        int check_year = t->tm_year + 1900;
        int check_month = t->tm_mon + 1;
        int check_day = t->tm_mday + day_offset;

        if (check_day > 31) {
            check_day -= 31;
            check_month++;
            if (check_month > 12) {
                check_month = 1;
                check_year++;
            }
        }

        int weekday = get_weekday(check_year, check_month, check_day);

        printf("\n%d年%d月%d日 (%s):\n",
               check_year, check_month, check_day, weekday_names[weekday]);

        for (int court = 1; court <= MAX_COURTS; court++) {
            printf("场地%d: ", court);

            Node* current = head;
            int found = 0;
            while (current != NULL) {
                if (current->court == court && current->year == check_year &&
                    current->month == check_month && current->day == check_day) {
                    printf("%02d:00[已约] ", current->hour);
                    found = 1;
                }
                current = current->next;
            }

            if (!found) {
                printf("[全部可约]");
            }
            printf("\n");
        }
    }
    printf("\n");
}

void search_info() {
    printf("\n=== 信息查询 ===\n");
    printf("1. 按场地查询\n");
    printf("2. 按手机号查询\n");

    int choice;
    do {
        printf("请选择查询方式(1-2): ");
        scanf("%d", &choice);
        if (choice != 1 && choice != 2) {
            printf("无效选择！请输入1或2。\n");
        }
    } while (choice != 1 && choice != 2);

    if (choice == 1) {
        int court;
        do {
            printf("请输入场地编号(1-8): ");
            scanf("%d", &court);
            if (court < 1 || court > MAX_COURTS) {
                printf("场地编号无效！请输入1-8之间的数字。\n");
            }
        } while (court < 1 || court > MAX_COURTS);

        printf("\n场地%d的预约记录:\n", court);
        printf("----------------------------------------\n");

        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (current->court == court) {
                printf("%d年%d月%d日 %02d:00 - %s(%s) - %s\n",
                       current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id, current->user.phone);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("该场地暂无预约记录\n");
        }
        printf("----------------------------------------\n");
    }
    else if (choice == 2) {
        char phone[15];
        do {
            printf("请输入手机号: ");
            scanf("%s", phone);
            if (!check_phone(phone)) {
                printf("手机号格式错误！请输入11位数字，以1开头。\n");
            }
        } while (!check_phone(phone));

        printf("\n手机号%s的预约记录:\n", phone);
        printf("----------------------------------------\n");

        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (strcmp(current->user.phone, phone) == 0) {
                printf("场地%d - %d年%d月%d日 %02d:00 - %s(%s)\n",
                       current->court, current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("该手机号暂无预约记录\n");
        }
        printf("----------------------------------------\n");
    }
}

void cancel_booking() {
    printf("\n=== 取消预约 ===\n");

    int court;
    char phone[15];

    do {
        printf("请输入场地编号(1-8): ");
        scanf("%d", &court);
        if (court < 1 || court > MAX_COURTS) {
            printf("场地编号无效！请输入1-8之间的数字。\n");
        }
    } while (court < 1 || court > MAX_COURTS);

    do {
        printf("请输入预约时的手机号: ");
        scanf("%s", phone);
        if (!check_phone(phone)) {
            printf("手机号格式错误！请输入11位数字，以1开头。\n");
        }
    } while (!check_phone(phone));

    Node* current = head;
    Node* prev = NULL;

    while (current != NULL) {
        if (current->court == court && strcmp(current->user.phone, phone) == 0) {
            printf("\n找到预约记录：\n");
            printf("场地%d - %d年%d月%d日 %02d:00 - %s(%s)\n",
                   current->court, current->year, current->month, current->day, current->hour,
                   current->user.name, current->user.id);

            if (prev == NULL) {
                head = current->next;
            } else {
                prev->next = current->next;
            }

            free(current);

            printf("预约取消成功！\n");

            save_file();
            return;
        }

        prev = current;
        current = current->next;
    }

    printf("未找到匹配的预约记录！\n");
    printf("请检查场地编号和手机号是否正确。\n");
}

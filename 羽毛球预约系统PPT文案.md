# 羽毛球预约系统 PPT文案

## 封面页
**羽毛球馆预约系统**
*基于C语言的智能预约管理平台*

---

## 01 设计任务 (3页)

### 1.1 项目背景
- **现状问题**
  - 传统人工预约效率低下
  - 场地冲突频繁发生
  - 管理成本高，易出错

- **解决方案**
  - 开发自动化预约系统
  - 提高场地利用率
  - 规范预约流程

### 1.2 系统目标
- **功能目标**
  - 实现场地在线预约
  - 提供状态查询功能
  - 支持预约管理操作

- **技术目标**
  - 使用C语言开发
  - 采用模块化设计
  - 实现数据持久化

### 1.3 应用场景
- **目标用户**：校内师生
- **使用环境**：上海理工大学1100校区
- **服务范围**：8个羽毛球场地
- **开放时间**：
  - 工作日：15:00-21:00
  - 周末：09:00-21:00

---

## 02 方案确定 (4页)

### 2.1 系统架构
```
用户交互层
    ↓
业务逻辑层
    ↓
数据存储层
```

- **用户交互层**：菜单界面、输入验证
- **业务逻辑层**：预约管理、时间验证
- **数据存储层**：文件存储、链表管理

### 2.2 数据结构设计
**用户结构体 (User)**
```c
typedef struct {
    char id[20];      // 工号/学号
    char name[50];    // 姓名
    char phone[15];   // 手机号
} User;
```

**预约结构体 (Node)**
```c
typedef struct Node {
    int court;        // 场地编号
    int year, month, day, hour;  // 时间
    User user;        // 用户信息
    struct Node* next; // 链表指针
} Node;
```

### 2.3 功能模块划分
- **核心功能**
  - 预约录入 (add_booking)
  - 状态显示 (show_status)
  - 信息查询 (search_info)
  - 预约取消 (cancel_booking)

- **辅助功能**
  - 时间验证 (check_time)
  - 日期检查 (check_date)
  - 文件操作 (save_file/load_file)

### 2.4 技术选型
- **开发语言**：C语言
- **数据结构**：单向链表
- **存储方式**：文本文件
- **编译环境**：GCC编译器

---

## 03 算法设计 (4页)

### 3.1 核心数据结构算法
**链表管理算法**
- **插入算法**：头插法，时间复杂度O(1)
```c
Node* new_node = malloc(sizeof(Node));
new_node->next = head;
head = new_node;
```

- **查找算法**：线性查找，时间复杂度O(n)
```c
while (current != NULL) {
    if (匹配条件) return current;
    current = current->next;
}
```

- **删除算法**：双指针法，维护前驱节点
```c
if (prev == NULL) head = current->next;
else prev->next = current->next;
free(current);
```

### 3.2 时间处理算法
**日期验证算法**
- 基础范围检查：年份(2024-2030)、月份(1-12)、日期(1-31)
- 闰年判断：`(year%4==0 && year%100!=0) || (year%400==0)`
- 月份天数验证：使用天数数组配合闰年处理

**星期计算算法（蔡勒公式）**
```c
// 1-2月处理为上年13-14月
if (month < 3) { month += 12; year--; }
k = year % 100;  // 年份后两位
j = year / 100;  // 世纪数
h = (day + (13*(month+1))/5 + k + k/4 + j/4 - 2*j) % 7;
return (h + 5) % 7 + 1;  // 转换为1-7表示周一到周日
```

### 3.3 业务逻辑算法
**时间有效性验证算法**
```
输入验证 → 当前时间比较 → 星期判断 → 开放时间检查 → 教工时间排除
```

**验证流程**：
1. **基础验证**：格式和范围检查
2. **时间比较**：不能预约过去时间
3. **开放时间判断**：
   - 工作日(1-5)：15:00-21:00
   - 周末(6-7)：09:00-21:00
4. **特殊时间排除**：周一三五17:00-20:00为教工时间

**预约冲突检测算法**
```c
// 遍历链表检查时间冲突
while (current != NULL) {
    if (current->court == court &&
        current->year == year && current->month == month &&
        current->day == day && current->hour == hour) {
        return -1;  // 冲突
    }
    current = current->next;
}
```

### 3.4 文件操作与显示算法
**文件I/O算法**
- **存储格式**：`场地|年|月|日|时|工号|姓名|手机号`
- **保存算法**：
```c
while (current != NULL) {
    fprintf(file, "%d|%d|%d|%d|%d|%s|%s|%s\n",
            court, year, month, day, hour, id, name, phone);
    current = current->next;
}
```

- **加载算法**：
```c
while (fgets(line, sizeof(line), file)) {
    sscanf(line, "%d|%d|%d|%d|%d|%s|%s|%s",
           &court, &year, &month, &day, &hour, id, name, phone);
    add_node(court, year, month, day, hour, user);
}
```

**状态显示算法**
- **三天循环**：从当前日期开始，连续显示3天
- **时间表生成**：8:00-20:00每小时一行
- **状态判断**：遍历链表匹配预约记录
- **格式化输出**：表格形式，对齐显示

---

## 04 心得体会 (3页)

### 4.1 技术收获
- **数据结构应用**
  - 深入理解链表的动态特性
  - 掌握结构体的复合使用
  - 学会内存管理的重要性

- **算法设计能力**
  - 时间验证逻辑的复杂处理
  - 蔡勒公式的数学应用
  - 文件I/O的高效实现

### 4.2 开发经验
- **模块化思维**
  - 功能分离，职责明确
  - 便于调试和维护
  - 提高代码复用性

- **用户体验设计**
  - 友好的中文界面
  - 完善的输入验证
  - 清晰的错误提示

### 4.3 问题与解决
**遇到的挑战**：
- 复杂的时间规则验证
- 链表内存泄漏问题
- 文件数据格式设计

**解决方案**：
- 分层验证，逐步筛选
- 严格的malloc/free配对
- 统一的分隔符格式

---

## 05 参考文献 (1页)

### 参考资料
1. 谭浩强. C程序设计(第五版). 清华大学出版社, 2017
2. 严蔚敏, 吴伟民. 数据结构(C语言版). 清华大学出版社, 2016
3. Brian W. Kernighan, Dennis M. Ritchie. C程序设计语言(第2版). 机械工业出版社, 2019
4. 蔡勒公式相关数学资料
5. C语言文件操作相关技术文档

### 在线资源
- C语言标准库函数参考手册
- 链表数据结构实现教程
- 时间处理算法相关资料

---

## 结束页
**谢谢观看！**

*Questions & Discussion*
